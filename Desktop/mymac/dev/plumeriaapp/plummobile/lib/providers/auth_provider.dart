import 'package:flutter/foundation.dart';
import '../models/staff_model.dart';
import '../models/auth_model.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final StorageService _storageService = StorageService();

  Staff? _currentStaff;
  AuthTokens? _tokens;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  Staff? get currentStaff => _currentStaff;
  AuthTokens? get tokens => _tokens;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize auth state from storage
  Future<void> initializeAuth() async {
    _setLoading(true);
    try {
      final storedTokens = await _storageService.getAuthTokens();
      if (storedTokens != null) {
        _tokens = storedTokens;
        _isAuthenticated = true;

        // Load staff data from storage first
        final storedStaff = await _storageService.getStaffData();
        if (storedStaff != null) {
          _currentStaff = storedStaff;
          notifyListeners();
        }

        // Optionally validate token and get fresh profile data
        await getProfile();
      }
    } catch (e) {
      _setError('Failed to initialize authentication');
    } finally {
      _setLoading(false);
    }
  }

  // Login with mobile and password
  Future<bool> login(String mobile, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.login(mobile, password);

      if (response.success &&
          response.staff != null &&
          response.tokens != null) {
        _currentStaff = response.staff;
        _tokens = response.tokens;
        _isAuthenticated = true;

        // Store tokens securely
        await _storageService.saveAuthTokens(response.tokens!);
        await _storageService.saveStaffData(response.staff!);

        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Send OTP
  Future<bool> sendOTP(String mobile) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.sendOTP(mobile);

      if (response.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Verify OTP
  Future<bool> verifyOTP(String mobile, String otp) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.verifyOTP(mobile, otp);

      if (response.success &&
          response.staff != null &&
          response.tokens != null) {
        _currentStaff = response.staff;
        _tokens = response.tokens;
        _isAuthenticated = true;

        // Store tokens securely
        await _storageService.saveAuthTokens(response.tokens!);
        await _storageService.saveStaffData(response.staff!);

        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Set password
  Future<bool> setPassword(
    String mobile,
    String password,
    String confirmPassword,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.setPassword(
        mobile,
        password,
        confirmPassword,
      );

      if (response.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Request password reset
  Future<bool> requestPasswordReset(String mobile) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.requestPasswordReset(mobile);

      if (response.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Verify password reset
  Future<bool> verifyPasswordReset(
    String mobile,
    String resetCode,
    String newPassword,
    String confirmPassword,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.verifyPasswordReset(
        mobile,
        resetCode,
        newPassword,
        confirmPassword,
      );

      if (response.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Get profile
  Future<bool> getProfile() async {
    if (!_isAuthenticated || _tokens == null) return false;

    try {
      final response = await _authService.getProfile();

      if (response.success && response.data != null) {
        _currentStaff = Staff.fromJson(response.data);
        await _storageService.saveStaffData(_currentStaff!);
        notifyListeners();
        return true;
      } else {
        if (response.statusCode == 401) {
          // Token expired, try refresh
          await refreshToken();
        }
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    if (_tokens?.refreshToken == null) return false;

    try {
      final response = await _authService.refreshToken(_tokens!.refreshToken);

      if (response.success && response.data?['tokens'] != null) {
        _tokens = AuthTokens.fromJson(response.data['tokens']);
        await _storageService.saveAuthTokens(_tokens!);
        notifyListeners();
        return true;
      } else {
        await logout();
        return false;
      }
    } catch (e) {
      await logout();
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      // Call logout API
      await _authService.logout();
    } catch (e) {
      // Continue with logout even if API call fails
    }

    // Clear local data
    await _storageService.clearAuthData();

    _currentStaff = null;
    _tokens = null;
    _isAuthenticated = false;
    _clearError();
    _setLoading(false);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
