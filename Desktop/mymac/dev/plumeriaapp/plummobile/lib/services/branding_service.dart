import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/branding_model.dart';
import 'api_service.dart';

class BrandingService {
  static const String _cacheKey = 'app_branding';
  static const String _versionKey = 'branding_version';
  static const Duration _cacheExpiry = Duration(hours: 24);
  
  static BrandingConfig? _cachedBranding;
  static final ApiService _apiService = ApiService();

  /// Get current branding configuration
  /// Tries API first, falls back to cache, then default config
  static Future<BrandingConfig> getBranding({bool forceRefresh = false}) async {
    try {
      // Return cached version if available and not forcing refresh
      if (_cachedBranding != null && !forceRefresh) {
        return _cachedBranding!;
      }

      // Check if we need to refresh from API
      if (!forceRefresh && await _isCacheValid()) {
        final cached = await _getCachedBranding();
        if (cached != null) {
          _cachedBranding = cached;
          return cached;
        }
      }

      // Fetch from API
      final response = await _apiService.get('/branding');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final branding = BrandingConfig.fromJson(response.data['data']);
        
        // Cache the result
        await _cacheBranding(branding);
        _cachedBranding = branding;
        
        return branding;
      } else {
        throw Exception('Failed to fetch branding: ${response.data['message']}');
      }
    } catch (e) {
      print('Error fetching branding from API: $e');
      
      // Try to get cached version
      final cached = await _getCachedBranding();
      if (cached != null) {
        _cachedBranding = cached;
        return cached;
      }
      
      // Return default configuration as last resort
      final defaultConfig = BrandingConfig.defaultConfig();
      _cachedBranding = defaultConfig;
      return defaultConfig;
    }
  }

  /// Check branding version to see if cache needs updating
  static Future<BrandingVersion?> checkVersion() async {
    try {
      final response = await _apiService.get('/branding/version');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return BrandingVersion.fromJson(response.data['data']);
      }
      return null;
    } catch (e) {
      print('Error checking branding version: $e');
      return null;
    }
  }

  /// Update branding configuration (Admin only)
  static Future<bool> updateBranding(BrandingConfig branding, String adminKey) async {
    try {
      final response = await _apiService.put(
        '/branding',
        branding.toJson(),
        headers: {'x-admin-key': adminKey},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        // Clear cache to force refresh
        await _clearCache();
        _cachedBranding = null;
        return true;
      }
      return false;
    } catch (e) {
      print('Error updating branding: $e');
      return false;
    }
  }

  /// Cache branding configuration locally
  static Future<void> _cacheBranding(BrandingConfig branding) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final brandingJson = jsonEncode(branding.toJson());
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      await prefs.setString(_cacheKey, brandingJson);
      await prefs.setInt('${_cacheKey}_timestamp', timestamp);
      await prefs.setInt(_versionKey, branding.version);
    } catch (e) {
      print('Error caching branding: $e');
    }
  }

  /// Get cached branding configuration
  static Future<BrandingConfig?> _getCachedBranding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final brandingJson = prefs.getString(_cacheKey);
      
      if (brandingJson != null) {
        final brandingMap = jsonDecode(brandingJson) as Map<String, dynamic>;
        return BrandingConfig.fromJson(brandingMap);
      }
      return null;
    } catch (e) {
      print('Error getting cached branding: $e');
      return null;
    }
  }

  /// Check if cached branding is still valid
  static Future<bool> _isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('${_cacheKey}_timestamp');
      final cachedVersion = prefs.getInt(_versionKey);
      
      if (timestamp == null || cachedVersion == null) return false;
      
      // Check if cache has expired
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final isExpired = DateTime.now().difference(cacheTime) > _cacheExpiry;
      
      if (isExpired) return false;
      
      // Check if version has changed
      final currentVersion = await checkVersion();
      if (currentVersion != null && currentVersion.version != cachedVersion) {
        return false;
      }
      
      return true;
    } catch (e) {
      print('Error checking cache validity: $e');
      return false;
    }
  }

  /// Clear cached branding data
  static Future<void> _clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove('${_cacheKey}_timestamp');
      await prefs.remove(_versionKey);
    } catch (e) {
      print('Error clearing branding cache: $e');
    }
  }

  /// Force refresh branding from API
  static Future<BrandingConfig> refreshBranding() async {
    return getBranding(forceRefresh: true);
  }

  /// Get cached branding synchronously (may be null)
  static BrandingConfig? getCachedBrandingSync() {
    return _cachedBranding;
  }

  /// Initialize branding service (call this at app startup)
  static Future<void> initialize() async {
    try {
      await getBranding();
    } catch (e) {
      print('Error initializing branding service: $e');
    }
  }
}
